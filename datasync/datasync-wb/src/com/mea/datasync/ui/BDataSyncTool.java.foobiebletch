// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.workbench.tool.BWbTool;
import javax.baja.workbench.BWbShell;
import javax.baja.ui.CommandArtifact;

/**
 * Minimal Niagara Workbench tool that appears in Tools menu.
 */
@NiagaraType
public class BDataSyncTool extends BWbTool {



////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

    /**
     * Required Type field for all BObject subclasses.
     * This registers your class with the Niagara Type system.
     */
    public static final Type TYPE = Sys.loadType(BDataSyncTool.class);

    /**
     * Get the Type of this object.
     * @return the Type
     */
    public Type getType() {
//region /*+ ------------ BEGIN BAJA AUTO GENERATED CODE ------------ +*/
//@formatter:off
/*@ $com.mea.datasync.ui.BDataSyncTool(2979906276)1.0$ @*/
/* Generated Mon Jun 16 00:31:52 AEST 2025 by Slot-o-Matic (c) Tridium, Inc. 2012-2025 */

  //region Type

  @Override
  public Type getType() { return TYPE; }
  public static final Type TYPE = Sys.loadType(BDataSyncTool.class);

  //endregion Type

//@formatter:on
//endregion /*+ ------------ END BAJA AUTO GENERATED CODE -------------- +*/
        return TYPE;
    }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

    /**
     * Default constructor.
     */
    public BDataSyncTool() {
    }

////////////////////////////////////////////////////////////////
// BWbTool
////////////////////////////////////////////////////////////////

    /**
     * Override invoke to handle tool activation from Tools menu.
     * @param shell the workbench shell
     * @return CommandArtifact for undo support, or null
     */
    @Override
    public CommandArtifact invoke(BWbShell shell) {
        System.out.println("DataSync Tool invoked from Tools menu!");

        // Simple confirmation that the tool was invoked
        // In a real tool, you would open a dialog or view here

        return null;
    }
}
