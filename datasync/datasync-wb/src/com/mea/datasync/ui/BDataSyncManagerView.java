// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import javax.baja.gx.BInsets;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BButton;
import javax.baja.ui.BLabel;
import javax.baja.ui.BScrollPane;
import javax.baja.ui.BSeparator;
import javax.baja.ui.Command;
import javax.baja.ui.CommandArtifact;
import javax.baja.ui.enums.BOrientation;
import javax.baja.ui.event.BActionEvent;
import javax.baja.ui.event.BActionListener;
import javax.baja.ui.layout.BBorderLayout;
import javax.baja.ui.layout.BFlowLayout;
import javax.baja.ui.layout.BGridBagConstraints;
import javax.baja.ui.layout.BGridBagLayout;
import javax.baja.ui.pane.BPane;
import javax.baja.ui.toolbar.BToolBar;
import javax.baja.ui.toolbar.BIToolBar;
import javax.baja.ui.table.TableSelectionListener;
import javax.baja.ui.table.TableSelectionEvent;
import javax.baja.workbench.view.BWbView;
import javax.baja.workbench.view.BIExportableTableView;

import com.mea.datasync.model.ConnectionProfile;

/**
 * BDataSyncManagerView is the main view for the N4-DataSync tool.
 * It displays connection profiles in a table and provides controls
 * for managing profiles and performing synchronization operations.
 */
@NiagaraType
public class BDataSyncManagerView extends BWbView implements BIExportableTableView {

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

    public static final Type TYPE = Sys.loadType(BDataSyncManagerView.class);
    
    @Override
    public Type getType() { 
        return TYPE; 
    }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

    public BDataSyncManagerView() {
        initializeComponents();
        layoutComponents();
        setupEventHandlers();
    }

////////////////////////////////////////////////////////////////
// BWbView Implementation
////////////////////////////////////////////////////////////////

    @Override
    public BIToolBar getViewToolBar() {
        if (toolbar == null) {
            toolbar = new BToolBar();
            toolbar.add(newProfileButton);
            toolbar.add(editProfileButton);
            toolbar.add(deleteProfileButton);
            toolbar.addSeparator();
            toolbar.add(syncNowButton);
            toolbar.add(syncAllButton);
            toolbar.addSeparator();
            toolbar.add(refreshButton);
        }
        return toolbar;
    }

////////////////////////////////////////////////////////////////
// BIExportableTableView Implementation
////////////////////////////////////////////////////////////////

    @Override
    public BDataSyncTable getExportTable() {
        return profileTable;
    }

////////////////////////////////////////////////////////////////
// Component Initialization
////////////////////////////////////////////////////////////////

    private void initializeComponents() {
        // Create main table
        profileTable = new BDataSyncTable();
        
        // Create toolbar buttons
        newProfileButton = new BButton("New Profile");
        editProfileButton = new BButton("Edit Profile");
        deleteProfileButton = new BButton("Delete Profile");
        syncNowButton = new BButton("Sync Now");
        syncAllButton = new BButton("Sync All");
        refreshButton = new BButton("Refresh");
        
        // Create status panel components
        statusLabel = new BLabel("Ready");
        
        // Initially disable buttons that require selection
        editProfileButton.setEnabled(false);
        deleteProfileButton.setEnabled(false);
        syncNowButton.setEnabled(false);
    }

    private void layoutComponents() {
        setLayout(new BBorderLayout());
        
        // Main table in center with scroll pane
        BScrollPane scrollPane = new BScrollPane(profileTable);
        add(scrollPane, BBorderLayout.CENTER);
        
        // Status panel at bottom
        BPane statusPanel = createStatusPanel();
        add(statusPanel, BBorderLayout.SOUTH);
        
        // Info panel at top
        BPane infoPanel = createInfoPanel();
        add(infoPanel, BBorderLayout.NORTH);
    }

    private BPane createInfoPanel() {
        BPane panel = new BPane();
        panel.setLayout(new BFlowLayout(BFlowLayout.LEFT));
        panel.setInsets(new BInsets(5, 5, 5, 5));
        
        BLabel titleLabel = new BLabel("N4-DataSync Connection Profiles");
        titleLabel.setFont(titleLabel.getFont().deriveFont(14.0f));
        panel.add(titleLabel);
        
        return panel;
    }

    private BPane createStatusPanel() {
        BPane panel = new BPane();
        panel.setLayout(new BBorderLayout());
        panel.setInsets(new BInsets(2, 5, 2, 5));
        
        // Add separator at top
        panel.add(new BSeparator(BOrientation.HORIZONTAL), BBorderLayout.NORTH);
        
        // Status label
        panel.add(statusLabel, BBorderLayout.WEST);
        
        return panel;
    }

////////////////////////////////////////////////////////////////
// Event Handlers
////////////////////////////////////////////////////////////////

    private void setupEventHandlers() {
        // New Profile button
        newProfileButton.addActionListener(new BActionListener() {
            @Override
            public void actionPerformed(BActionEvent event) {
                handleNewProfile();
            }
        });
        
        // Edit Profile button
        editProfileButton.addActionListener(new BActionListener() {
            @Override
            public void actionPerformed(BActionEvent event) {
                handleEditProfile();
            }
        });
        
        // Delete Profile button
        deleteProfileButton.addActionListener(new BActionListener() {
            @Override
            public void actionPerformed(BActionEvent event) {
                handleDeleteProfile();
            }
        });
        
        // Sync Now button
        syncNowButton.addActionListener(new BActionListener() {
            @Override
            public void actionPerformed(BActionEvent event) {
                handleSyncNow();
            }
        });
        
        // Sync All button
        syncAllButton.addActionListener(new BActionListener() {
            @Override
            public void actionPerformed(BActionEvent event) {
                handleSyncAll();
            }
        });
        
        // Refresh button
        refreshButton.addActionListener(new BActionListener() {
            @Override
            public void actionPerformed(BActionEvent event) {
                handleRefresh();
            }
        });
        
        // Table selection changes
        profileTable.getSelection().addTableSelectionListener(new TableSelectionListener() {
            @Override
            public void tableSelectionChanged(TableSelectionEvent event) {
                updateButtonStates();
            }
        });
    }

////////////////////////////////////////////////////////////////
// Action Handlers
////////////////////////////////////////////////////////////////

    private void handleNewProfile() {
        statusLabel.setText("Creating new profile...");
        // TODO: Open new profile dialog
        System.out.println("New Profile clicked");
    }

    private void handleEditProfile() {
        ConnectionProfile selected = profileTable.getSelectedProfile();
        if (selected != null) {
            statusLabel.setText("Editing profile: " + selected.getName());
            // TODO: Open edit profile dialog
            System.out.println("Edit Profile clicked: " + selected.getName());
        }
    }

    private void handleDeleteProfile() {
        ConnectionProfile selected = profileTable.getSelectedProfile();
        if (selected != null) {
            statusLabel.setText("Deleting profile: " + selected.getName());
            // TODO: Confirm and delete profile
            System.out.println("Delete Profile clicked: " + selected.getName());
        }
    }

    private void handleSyncNow() {
        ConnectionProfile selected = profileTable.getSelectedProfile();
        if (selected != null) {
            statusLabel.setText("Synchronizing profile: " + selected.getName());
            // TODO: Start sync operation
            System.out.println("Sync Now clicked: " + selected.getName());
        }
    }

    private void handleSyncAll() {
        statusLabel.setText("Synchronizing all profiles...");
        // TODO: Start sync all operation
        System.out.println("Sync All clicked");
    }

    private void handleRefresh() {
        statusLabel.setText("Refreshing profile list...");
        // TODO: Refresh profile data
        System.out.println("Refresh clicked");
        statusLabel.setText("Ready");
    }

    private void updateButtonStates() {
        ConnectionProfile selected = profileTable.getSelectedProfile();
        boolean hasSelection = (selected != null);
        
        editProfileButton.setEnabled(hasSelection);
        deleteProfileButton.setEnabled(hasSelection);
        syncNowButton.setEnabled(hasSelection);
    }

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

    private BDataSyncTable profileTable;
    private BToolBar toolbar;
    
    // Toolbar buttons
    private BButton newProfileButton;
    private BButton editProfileButton;
    private BButton deleteProfileButton;
    private BButton syncNowButton;
    private BButton syncAllButton;
    private BButton refreshButton;
    
    // Status components
    private BLabel statusLabel;
}
