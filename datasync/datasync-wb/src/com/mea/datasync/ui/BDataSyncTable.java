// In: com.mea.datasync.ui
package com.mea.datasync.ui;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

import javax.baja.gx.BColor;
import javax.baja.gx.BImage;
import javax.baja.nre.annotations.NiagaraType;
import javax.baja.sys.BAbsTime;
import javax.baja.sys.Sys;
import javax.baja.sys.Type;
import javax.baja.ui.BMenu;
import javax.baja.ui.event.BMouseEvent;
import javax.baja.ui.table.BTable;
import javax.baja.ui.table.DynamicTableModel;
import javax.baja.ui.table.TableCellRenderer;
import javax.baja.ui.table.TableController;
import javax.baja.ui.table.TableModel;
import javax.baja.ui.table.TableSelection;

import com.mea.datasync.model.ConnectionProfile;

/**
 * BDataSyncTable displays connection profiles and their sync status
 * in a table format following Niagara UI patterns.
 */
@NiagaraType
public class BDataSyncTable extends BTable {

////////////////////////////////////////////////////////////////
// Type
////////////////////////////////////////////////////////////////

    public static final Type TYPE = Sys.loadType(BDataSyncTable.class);
    
    @Override
    public Type getType() { 
        return TYPE; 
    }

////////////////////////////////////////////////////////////////
// Constructor
////////////////////////////////////////////////////////////////

    public BDataSyncTable() {
        // Initialize with dummy data
        this.profiles = createDummyData();
        
        // Set up table components following Niagara pattern
        setModel(new DynamicTableModel(new Model()));
        setSelection(new Selection());
        setController(new Controller());
        setCellRenderer(new CellRenderer());
        setMultipleSelection(true);
    }

////////////////////////////////////////////////////////////////
// Data Management
////////////////////////////////////////////////////////////////

    /**
     * Create dummy data for demonstration
     */
    private List<ConnectionProfile> createDummyData() {
        List<ConnectionProfile> data = new ArrayList<>();
        
        // Profile 1 - Successful sync
        ConnectionProfile profile1 = new ConnectionProfile(
            "Building A HVAC", 
            "Excel", 
            "C:\\Data\\BuildingA_HVAC.xlsx",
            "Equipment",
            "*************",
            "admin",
            "station:|slot:/Drivers"
        );
        profile1.setLastSync(BAbsTime.now().subtract(BAbsTime.makeRelTime(2, 0, 0, 0))); // 2 hours ago
        profile1.setStatus(ConnectionProfile.SyncStatus.SUCCESS);
        profile1.setComponentsCreated(45);
        data.add(profile1);
        
        // Profile 2 - Error state
        ConnectionProfile profile2 = new ConnectionProfile(
            "Building B Lighting",
            "Excel",
            "C:\\Data\\BuildingB_Lighting.xlsx", 
            "Points",
            "*************",
            "engineer",
            "station:|slot:/Drivers/Lighting"
        );
        profile2.setLastSync(BAbsTime.now().subtract(BAbsTime.makeRelTime(24, 0, 0, 0))); // 1 day ago
        profile2.setStatus(ConnectionProfile.SyncStatus.ERROR);
        profile2.setLastError("Connection timeout to target station");
        data.add(profile2);
        
        // Profile 3 - Never synced
        ConnectionProfile profile3 = new ConnectionProfile(
            "Chiller Plant",
            "Excel",
            "C:\\Data\\ChillerPlant.xlsx",
            "Chillers", 
            "192.168.1.102",
            "admin",
            "station:|slot:/Drivers/HVAC"
        );
        data.add(profile3);
        
        return data;
    }

    /**
     * Get the selected connection profile or null if no selection.
     */
    public ConnectionProfile getSelectedProfile() {
        int sel = getSelection().getRow();
        if (sel < 0 || sel >= profiles.size()) return null;
        return profiles.get(sel);
    }

    /**
     * Get the selected connection profiles or an empty array.
     */
    public ConnectionProfile[] getSelectedProfiles() {
        int[] sel = getSelection().getRows();
        ConnectionProfile[] result = new ConnectionProfile[sel.length];
        for (int i = 0; i < sel.length; i++) {
            if (sel[i] >= 0 && sel[i] < profiles.size()) {
                result[i] = profiles.get(sel[i]);
            }
        }
        return result;
    }

////////////////////////////////////////////////////////////////
// Table Model
////////////////////////////////////////////////////////////////

    class Model extends TableModel {
        
        @Override
        public int getRowCount() {
            return profiles.size();
        }
        
        @Override
        public int getColumnCount() {
            return COLUMN_NAMES.length;
        }
        
        @Override
        public String getColumnName(int col) {
            return COLUMN_NAMES[col];
        }
        
        @Override
        public Object getValueAt(int row, int col) {
            if (row < 0 || row >= profiles.size()) return "";
            
            ConnectionProfile profile = profiles.get(row);
            switch (col) {
                case COL_NAME: return profile.getName();
                case COL_SOURCE: return profile.getSourcePath();
                case COL_TARGET: return profile.getTargetHost();
                case COL_STATUS: return profile.getStatus();
                case COL_LAST_SYNC: 
                    BAbsTime lastSync = profile.getLastSync();
                    if (lastSync == null) return "Never";
                    return DATE_FORMAT.format(lastSync.getJavaDate());
                case COL_COMPONENTS: return profile.getComponentsCreated();
                default: return "";
            }
        }
        
        @Override
        public boolean isColumnSortable(int col) {
            return true;
        }
    }

////////////////////////////////////////////////////////////////
// Selection
////////////////////////////////////////////////////////////////

    class Selection extends TableSelection {
        @Override
        public void updateTable() {
            super.updateTable();
            // Update any command states here if needed
        }
    }

////////////////////////////////////////////////////////////////
// Controller
////////////////////////////////////////////////////////////////

    class Controller extends TableController {
        
        @Override
        public void cellDoubleClicked(BMouseEvent event, int row, int col) {
            if (row >= 0 && row < profiles.size()) {
                ConnectionProfile profile = profiles.get(row);
                System.out.println("Double-clicked profile: " + profile.getName());
                // TODO: Open profile editor or sync view
            }
        }
        
        @Override
        protected BMenu makePopup() {
            BMenu menu = super.makePopup();
            // TODO: Add context menu items like "Edit Profile", "Sync Now", "Delete"
            return menu;
        }
    }

////////////////////////////////////////////////////////////////
// Cell Renderer
////////////////////////////////////////////////////////////////

    class CellRenderer extends TableCellRenderer {
        
        @Override
        public String getCellText(Cell cell) {
            Object value = cell.value;
            if (value == null) return "";
            return value.toString();
        }
        
        @Override
        public BColor getForeground(Cell cell) {
            // Color-code status column
            if (cell.column == COL_STATUS && cell.row < profiles.size()) {
                ConnectionProfile profile = profiles.get(cell.row);
                switch (profile.getStatus()) {
                    case SUCCESS: return BColor.make(0, 128, 0); // Green
                    case ERROR: return BColor.make(255, 0, 0);   // Red
                    case IN_PROGRESS: return BColor.make(255, 165, 0); // Orange
                    default: return super.getForeground(cell);
                }
            }
            return super.getForeground(cell);
        }
    }

////////////////////////////////////////////////////////////////
// Constants
////////////////////////////////////////////////////////////////

    private static final int COL_NAME = 0;
    private static final int COL_SOURCE = 1;
    private static final int COL_TARGET = 2;
    private static final int COL_STATUS = 3;
    private static final int COL_LAST_SYNC = 4;
    private static final int COL_COMPONENTS = 5;
    
    private static final String[] COLUMN_NAMES = {
        "Profile Name",
        "Source File", 
        "Target Station",
        "Status",
        "Last Sync",
        "Components"
    };
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("MM/dd/yyyy HH:mm");

////////////////////////////////////////////////////////////////
// Attributes
////////////////////////////////////////////////////////////////

    private List<ConnectionProfile> profiles;
}
