/*
 * Copyright 2001 Tridium, Inc. All Rights Reserved.
 */
package javax.baja.ui.text;

import javax.baja.gx.*;
import com.tridium.ui.theme.*;

/**
 * PasswordRenderer is used to paint the text document characters
 * with a special character so that the text cannot be read.
 *
 * <AUTHOR>       
 * @creation  1 Dec 01
 * @version   $Revision: 7$ $Date: 3/28/05 10:32:34 AM EST$
 * @since     Baja 1.0
 */
public class PasswordRenderer
  extends TextRenderer
{   

  public BBrush getForeground(Segment seg)
  {                              
    // disabled color
    if (!getEditor().isEnabled() ||
        !getEditor().getParentWidget().isEnabled())
      return Theme.textEditor().getDisabledTextBrush();
      
    return getOptions().getColorCoding().getForeground().toBrush();
  }

  double paintSegment(Graphics g, double x, char[] buffer, 
                   int type, int mods, int offset, int length)
  { 
    double sum = 0;
    
    for(int i = 0; i < buffer.length; i++)
      sum += font.width(buffer[i]);
    
    double cellWidth = sum / buffer.length;
    
    for(int i=0; i<length; ++i) drawBullet(g, x + i*cellWidth);
    return (length * cellWidth);
  }
  
  void drawBullet(Graphics g, double x)
  {
    x = x+1;
    double y = (cellHeight - 5)/2;
    g.fillRect(x+1, y, 3, 5);
    g.fillRect(x, y+1, 5, 3);
  }
  
}
